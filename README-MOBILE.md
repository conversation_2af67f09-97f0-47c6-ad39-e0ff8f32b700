# Mobile开发指南

## 项目结构

```
src/modules/mobile/
├── components/          # 公共组件
│   └── Header.vue      # 统一Header组件
├── views/              # 页面组件
│   ├── Login/
│   ├── Register/
│   └── ForgotPassword/
├── layout/             # 布局组件
├── router/             # 路由配置
├── utils/              # 工具函数
│   └── rem.ts         # rem适配工具
└── main.ts            # 入口文件
```

## 开发命令

### 启动开发服务器
```bash
# PC端开发 (端口5173)
npm run dev

# Mobile端开发 (端口5174)
npm run dev:mobile
```

### 构建项目
```bash
# 构建PC端
npm run build

# 构建Mobile端
npm run build:mobile

# 构建所有端
npm run build:all
```

### 预览构建结果
```bash
# 预览PC端
npm run preview

# 预览Mobile端
npm run preview:mobile
```

## px转rem配置

### 自动转换规则
- **rootValue**: 37.5 (基于375px设计稿)
- **最小转换值**: 2px (小于2px的不转换)
- **排除选择器**: `.van-`, `.el-`, `html`, `body`, `.no-rem`

### 使用方法
1. **正常写px**: 在样式中正常写px单位，构建时会自动转换为rem
2. **不转换的情况**: 
   - Vant组件样式 (`.van-`)
   - Element Plus组件样式 (`.el-`)
   - 添加`.no-rem`类的元素

### 示例
```scss
.my-component {
  width: 375px;        // 转换为 10rem
  height: 200px;       // 转换为 5.33333rem
  font-size: 16px;     // 转换为 0.42667rem
  border: 1px solid;   // 1px不转换，保持1px
}

.no-rem {
  width: 100px;        // 不转换，保持100px
}
```

## Header组件使用

### 基本用法
```vue
<template>
  <Header 
    title="页面标题"
    :show-back="true"
    :show-search="false"
    @back="handleBack"
  />
</template>
```

### Props配置
- `title`: 标题文字 (默认: 'GlocalMe')
- `showBack`: 显示返回按钮 (默认: false)
- `showClose`: 显示关闭按钮 (默认: false)
- `showSearch`: 显示搜索按钮 (默认: true)
- `showMenu`: 显示菜单按钮 (默认: true)

### 事件
- `@back`: 返回按钮点击
- `@close`: 关闭按钮点击
- `@search`: 搜索按钮点击
- `@menu`: 菜单按钮点击

## 页面适配

### 内容区域padding
使用`.mobile-page-content`类自动添加顶部间距：

```vue
<template>
  <div class="mobile-page-content">
    <!-- 页面内容 -->
  </div>
</template>
```

### 设计稿适配
- **设计稿宽度**: 375px
- **根字体大小**: 37.5px
- **转换比例**: 1px = 1/37.5 rem

## 开发注意事项

1. **样式单位**: 使用px单位，构建时自动转换为rem
2. **组件库**: 使用Vant组件库，样式不会被转换
3. **图标**: 使用Vant内置图标或自定义SVG图标
4. **路由**: 在`src/modules/mobile/router/`中配置
5. **状态管理**: 使用Pinia，与PC端共享store

## 构建配置

### vite.mobile.config.ts
- 专门的mobile构建配置
- 集成postcss-pxtorem插件
- 优化mobile端依赖打包
- 输出到`dist/mobile`目录

### TypeScript配置
- 支持import.meta语法
- 模块解析配置
- 路径别名配置

## 调试技巧

1. **Chrome DevTools**: 使用移动设备模拟器
2. **rem调试**: 在控制台查看根字体大小
3. **响应式测试**: 测试不同屏幕尺寸
4. **性能优化**: 使用Lighthouse分析

## 部署说明

### 构建产物
```
dist/
├── web/           # PC端构建产物
└── mobile/        # Mobile端构建产物
```

### 服务器配置
- PC端: 部署到根目录
- Mobile端: 部署到`/mobile`子目录
- 或者使用不同的域名/子域名
