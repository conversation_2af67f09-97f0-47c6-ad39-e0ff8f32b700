{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "noImplicitAny": false, "paths": {"@/*": ["./src/*"], "@web/*": ["./src/modules/web/*"], "@mobile/*": ["./src/modules/mobile/*"]}}}