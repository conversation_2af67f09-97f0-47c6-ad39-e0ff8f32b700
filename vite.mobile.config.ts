import { fileURLToPath, URL } from 'node:url'
import { dirname, resolve } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

import { createSvgIconsPlugin } from "vite-plugin-svg-icons"
import postcssPxToRem from 'postcss-pxtorem'

const __dirname = dirname(fileURLToPath(import.meta.url))
const root = process.cwd()
function pathResolve(dir: string) {
  return resolve(root, ".", dir)
}

// Mobile专用配置
export default defineConfig(({ command }) => ({
  plugins: [
    vue(),
    vueDevTools(),
    createSvgIconsPlugin({
      iconDirs: [pathResolve("src/assets/svgs")],
      symbolId: "icon-[dir]-[name]",
      svgoOptions: true,
    }),
  ],
  
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@mobile': fileURLToPath(new URL('./src/modules/mobile', import.meta.url)),
    },
  },
  
  // 不设置root，使用默认根目录
  base: './',

  // CSS配置
  css: {
    postcss: {
      plugins: [
        postcssPxToRem({
          rootValue: 37.5,
          propList: ['*'],
          
          // 不需要转换的选择器
          selectorBlackList: [
            'html', // html标签不转换
            'body', // body标签不转换
            '.no-rem', // 自定义不转换的类
          ],
          
          // 替换规则
          replace: true,
          
          // 是否转换媒体查询中的px
          mediaQuery: false,
          
          // 设置最小转换数值，小于这个值的px不会被转换
          minPixelValue: 2,
          
          // 排除文件夹或文件
          exclude: /node_modules/i,
          
          // 单位精度
          unitPrecision: 5,
        })
      ]
    }
  },
  
  // 开发服务器配置
  server: {
    port: 5174, // 使用不同的端口避免冲突
    host: true,
    open: command === 'serve' ? '/mobile/' : true,
  },
  
  // 优化配置
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'vue-i18n',
      'vant'
    ],
    force: true
  },
  
  // 构建配置
  build: {
    outDir: 'dist/mobile',
    emptyOutDir: true,
    rollupOptions: {
      input: resolve(__dirname, 'mobile/index.html'),
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          vant: ['vant'],
        },
      },
    },
  },
  
  // 定义全局常量
  define: {
    __VUE_I18N_FULL_INSTALL__: true,
    __VUE_I18N_LEGACY_API__: false,
    __INTLIFY_PROD_DEVTOOLS__: false,
  },
}))
