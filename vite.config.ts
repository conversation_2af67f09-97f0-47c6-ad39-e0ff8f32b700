import { fileURLToPath, URL } from 'node:url'
import { dirname, resolve } from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { VantResolver } from '@vant/auto-import-resolver'
import { createSvgIconsPlugin, } from "vite-plugin-svg-icons"

const __dirname = dirname(fileURLToPath(import.meta.url))

const root = process.cwd()

function pathResolve(dir: string,) {
  return resolve(root, ".", dir,)
}
// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    createSvgIconsPlugin({
      iconDirs: [pathResolve("src/assets/svgs",),],
      symbolId: "icon-[dir]-[name]",
      svgoOptions: true,
    },),
    AutoImport({
      resolvers: [ElementPlusResolver(), VantResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver(), VantResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@web': fileURLToPath(new URL('./src/modules/web', import.meta.url)),
      '@mobile': fileURLToPath(new URL('./src/modules/mobile', import.meta.url)),
    },
  },
  base: './',
  build: {
    rollupOptions: {
      input: {
        web: resolve(__dirname, 'index.html'),
        mobile: resolve(__dirname, 'mobile/index.html'),
      },
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
        },
      },
    },
  },
})
