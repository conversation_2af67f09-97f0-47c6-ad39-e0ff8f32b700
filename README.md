# esim-store

eSim 商场

## Technology

This template should help get you started developing with Vue 3 in Vite.

## Plugins

- vue-router
- pinia
- vant
- element-plus
- vue-i18n
- @vueuse/core

## Structure

```
mobile/ 移动端html入口
├── index.html
index.html 电脑端html入口
```

```
src/
├── assets/ 静态资源
│   └── style、image
├── components 全局组件
├── hooks 全局hooks
├── locale 国际化配置
├── stores 状态管理Pinia
├── modules/
│   ├── web/ 电脑端
│   │   ├── App.vue
│   │   ├── main.ts
│   │   ├── router/
│   │   └── views/
│   └── mobile/ 移动端
│       ├── App.vue
│       ├── main.ts
│       └── router/
│   │   └── views/
├── plugins/ 工具插件
│   └── i18n.ts
└── utils/ 工具方法
    ├── metaTitle.ts
    └── permission.ts
```

## Development

```
npm install
```

```
npm run dev
```

## Build

在[vite.config.ts](./vite.config.ts)配置中，支持两端打包输出，也可分别构建输出。

```
build: {
    input: {
        web: resolve(__dirname, 'index.html'),
        mobile: resolve(__dirname, 'mobile/index.html'),
    },
    ...
}
```
