export default {
  plugins: {
    'postcss-pxtorem': {
      // 根元素字体大小，通常设置为设计稿宽度的1/10
      // 如果设计稿是375px，则设置为37.5
      // 如果设计稿是750px，则设置为75
      rootValue: 37.5,
      
      // 需要转换的CSS属性，* 表示所有属性
      propList: ['*'],
      
      // 不需要转换的选择器
      selectorBlackList: [
        '.van-', // vant组件不转换
        '.el-', // element-plus组件不转换
        'html', // html标签不转换
        'body', // body标签不转换
      ],
      
      // 替换规则
      replace: true,
      
      // 是否转换媒体查询中的px
      mediaQuery: false,
      
      // 设置最小转换数值，小于这个值的px不会被转换
      minPixelValue: 2,
      
      // 排除文件夹或文件
      exclude: /node_modules/i,
      
      // 包含文件夹或文件（优先级高于exclude）
      include: /src\/modules\/mobile/i,
    }
  }
}
