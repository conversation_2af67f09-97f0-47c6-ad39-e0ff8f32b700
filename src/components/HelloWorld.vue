<script setup lang="ts">
defineProps<{
  msg: string
}>()

import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>

<template>
  <div>
    <h1 class="green">{{ msg }}</h1>
    <i18n-t keypath="header.desc" tag="h3">
      <a href="https://vite.dev/" target="_blank" rel="noopener">{{ t('header.desc_vite') }}</a>
      <a href="https://vuejs.org/" target="_blank" rel="noopener">{{ t('header.desc_vue') }}</a>
    </i18n-t>
  </div>
</template>

<style scoped>
h1 {
  font-weight: 500;
  font-size: 2.6rem;
  position: relative;
  top: -10px;
}

h3 {
  font-size: 1.2rem;
}
</style>
