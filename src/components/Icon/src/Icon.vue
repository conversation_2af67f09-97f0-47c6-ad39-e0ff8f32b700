<script lang="ts" setup>
import type { IconTypes, } from "@/components/Icon"
import { ElIcon, } from "element-plus"
import { computed, } from "vue"

defineOptions({
  name: "Icon",
},)

const props = defineProps<IconTypes>()

const prefixCls = "svg-icon:"
const symbolId = computed(() => {
  return `#icon-${props.icon?.split("svg-icon:",)[1]}`
},)

const getIconifyStyle = computed(() => {
  const { color, size, } = props
  return {
    fontSize: `${size}px`,
    color,
  }
},)
</script>

<template>
  <ElIcon :class="prefixCls" :color="getIconifyStyle.color" :size="getIconifyStyle.fontSize">
    <svg  aria-hidden="true">
      <use :xlink:href="symbolId" />
    </svg>
  </ElIcon>
</template>

<style lang="scss" scoped>
.iconify {
  :deep(svg) {
    &:hover {
      color: v-bind(hoverColor) !important;
    }
  }
}

.iconify {
  &:hover {
    color: v-bind(hoverColor) !important;
  }
}
</style>
