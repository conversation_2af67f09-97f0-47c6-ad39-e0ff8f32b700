import type { Router } from 'vue-router'
import { useTitle } from '@vueuse/core'
import i18n from '@/plugins/i18n'

const titleTemplate = '%s - ' + i18n.global.t('app.title')
const pageTitle = useTitle('', { titleTemplate })

export const setMetaTitle = function (title: string) {
  pageTitle.value = (title && i18n.global.t(title)) || title
}

export const metaPageTitle = function (router: Router) {
  router.beforeEach((to, from, next) => {
    const meta = to.meta || {}

    setMetaTitle(<string>meta.title)
    next()
  })
}
