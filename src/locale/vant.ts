import { Locale } from 'vant'
import enUS from 'vant/es/locale/lang/en-US'
import zhCN from 'vant/es/locale/lang/zh-CN'

import { LOCALE } from './const'

/**
 * 加载vant的语言包
 * @param lang
 */
async function loadVantLocale(lang) {
  let locale
  switch (lang) {
    case LOCALE.en: {
      locale = enUS
      break
    }
    case LOCALE.zhCn: {
      locale = zhCN
      break
    }
    // 默认使用英语
    default: {
      locale = enUS
    }
  }
  if (locale) {
    Locale.use(lang, locale)
  }
}

export { loadVantLocale }
