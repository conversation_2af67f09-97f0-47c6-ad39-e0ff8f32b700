export default {
  app: {
    title: 'esim 商场',
  },
  header: {
    msg: '你好！',
    desc: '你已经用{0}+{1}成功的创建了项目。接下来是什么？',
    desc_vite: 'Vite',
    desc_vue: 'Vue 3',
    logo: 'LocalMe',
    search_placeholder: '搜索您的全球目的地',
  },
  nav: {
    home: '首页',
    about: '关于',
    mobile: '移动端',
    shop_plans: '购买套餐',
    my_esims: '我的eSIM',
    about_us: '关于我们',
    login: '登录',
  },
  login: {
    title: '您的邮箱地址是什么？',
    email_label: '邮箱',
    email_placeholder: '请输入',
    continue: '继续',
    agreement_text: '通过登录或创建账户，您同意',
    user_agreement: '用户协议',
    and: '和',
    privacy_policy: '隐私政策',
    or: '或',
    continue_with_google: '使用Google继续',
    continue_with_apple: '使用Apple继续',
    continue_with_mobile: '使用手机号继续',
  },
  register: {
    title: '创建您的账户',
    password_label: '密码',
    password_placeholder: '请输入密码',
    confirm_password_label: '确认密码',
    confirm_password_placeholder: '请再次输入密码',
    register_button: '注册',
    registering: '注册中...',
    email_error: '请输入正确的邮箱地址',
    password_error: '密码必须6-20位，包含字母、数字和符号',
    password_mismatch: '两次输入的密码不一致',
    register_success: '注册成功！',
    register_failed: '注册失败，请重试',
    user_exists: '用户已存在，请直接登录',
    google_register_developing: 'Google 注册功能开发中',
    apple_register_developing: 'Apple 注册功能开发中',
    go_to_login: '已有账户？去登录',
  },
  forgot_password: {
    title: '忘记密码',
    back: '返回',
    reset_title: '重置密码',
    subtitle: '我们将发送验证码到您的邮箱，请输入6位验证码',
    email_label: '邮箱',
    email_placeholder: '<EMAIL>',
    verification_code_label: '验证码',
    verification_code_placeholder: '请输入6位验证码',
    new_password_label: '新密码',
    new_password_placeholder: '请输入新密码',
    confirm_new_password_label: '确认密码',
    confirm_new_password_placeholder: '请再次输入新密码',
    send_code: '发送验证码',
    sending: '发送中...',
    resetting: '重置中...',
    save_button: '保存',
    code_error: '请输入6位数字验证码',
    password_requirements: '密码必须6-20位，包含字母、数字和符号中的至少两种。不能是您之前使用过的密码。',
    remember_password: '想起密码了？',
    back_to_login: '返回登录',
    code_sent_success: '验证码已发送',
    code_sent_failed: '验证码发送失败',
    reset_success: '密码重置成功',
    reset_failed: '密码重置失败',
  },
}
