export default {
  app: {
    title: 'esim 商场',
  },
  header: {
    msg: '你好！',
    desc: '你已经用{0}+{1}成功的创建了项目。接下来是什么？',
    desc_vite: 'Vite',
    desc_vue: 'Vue 3',
    logo: 'LocalMe',
    search_placeholder: '搜索您的全球目的地',
  },
  nav: {
    home: '首页',
    about: '关于',
    mobile: '移动端',
    shop_plans: '购买套餐',
    my_esims: '我的eSIM',
    about_us: '关于我们',
    login: '登录',
  },
  login: {
    title: '您的邮箱地址是什么？',
    email_label: '邮箱',
    email_placeholder: '请输入',
    continue: '继续',
    agreement_text: '通过登录或创建账户，您同意',
    user_agreement: '用户协议',
    and: '和',
    privacy_policy: '隐私政策',
    or: '或',
    continue_with_google: '使用Google继续',
    continue_with_apple: '使用Apple继续',
    continue_with_mobile: '使用手机号继续',
  },
  register: {
    title: '创建您的账户',
    password_label: '密码',
    password_placeholder: '请输入密码',
    confirm_password_label: '确认密码',
    confirm_password_placeholder: '请再次输入密码',
    register_button: '注册',
    registering: '注册中...',
    email_error: '请输入正确的邮箱地址',
    password_error: '密码必须6-20位，包含字母、数字和符号',
    password_mismatch: '两次输入的密码不一致',
    register_success: '注册成功！',
    register_failed: '注册失败，请重试',
    user_exists: '用户已存在，请直接登录',
    google_register_developing: 'Google 注册功能开发中',
    apple_register_developing: 'Apple 注册功能开发中',
    go_to_login: '已有账户？去登录',
  },
}
