

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router/index'
import i18n from '@/plugins/i18n'
import 'lib-flexible'

// 全量导入 Vant 组件和样式
import Vant from 'vant'
import 'vant/lib/index.css'

const app = createApp(App)
import 'virtual:svg-icons-register'

// 先导入基础样式
import '@/assets/main.css'
// 最后导入我们的自定义样式，确保能够覆盖 Vant 的默认样式
import './styles/index.scss'

// 注册 Vant 组件
app.use(Vant)
app.use(createPinia())
app.use(router)
app.use(i18n)
app.mount('#app')
