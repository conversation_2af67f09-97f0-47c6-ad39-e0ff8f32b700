// rem适配方案
// 设计稿宽度，通常为375px或750px
const DESIGN_WIDTH = 375

// 设置根元素字体大小
function setRootFontSize() {
  const deviceWidth = document.documentElement.clientWidth || window.innerWidth
  
  // 限制最大和最小宽度
  const width = Math.min(Math.max(deviceWidth, 320), 768)
  
  // 计算根元素字体大小
  const fontSize = (width / DESIGN_WIDTH) * 37.5
  
  // 设置根元素字体大小
  document.documentElement.style.fontSize = fontSize + 'px'
}

// 初始化
setRootFontSize()

// 监听窗口大小变化
window.addEventListener('resize', setRootFontSize)
window.addEventListener('orientationchange', setRootFontSize)

// 页面加载完成后再次设置
document.addEventListener('DOMContentLoaded', setRootFontSize)

export { setRootFontSize }
