// Mobile端 Vant 组件按需导入配置
// 在这个文件中指定需要使用的 Vant 组件

import { App } from 'vue'

// 按需导入 Vant 组件
import {
  // 基础组件
  Button,
  Cell,
  CellGroup,
  Icon,
  Image as VanImage,
  Row,
  Col,
  Popup,
  
  // 表单组件
  Field,
  Form,
  Checkbox,
  CheckboxGroup,
  Radio,
  RadioGroup,
  Switch,
  Stepper,
  Rate,
  Slider,
  Uploader,
  
  // 反馈组件
  ActionSheet,
  Dialog,
  DropdownMenu,
  DropdownItem,
  Loading,
  Notify,
  Overlay,
  PullRefresh,
  SwipeCell,
  Toast,
  
  // 展示组件
  Circle,
  Collapse,
  CollapseItem,
  CountDown,
  Divider,
  Empty,
  ImagePreview,
  Lazyload,
  List,
  NoticeBar,
  Progress,
  Skeleton,
  Steps,
  Step,
  Sticky,
  Swipe,
  SwipeItem,
  Tag,
  
  // 导航组件
  Grid,
  GridItem,
  IndexBar,
  IndexAnchor,
  NavBar,
  Pagination,
  Sidebar,
  SidebarItem,
  Tab,
  Tabs,
  Tabbar,
  TabbarItem,
  TreeSelect,
  
  // 业务组件
  AddressEdit,
  AddressList,
  Area,
  Card,
  ContactCard,
  ContactEdit,
  ContactList,
  Coupon,
  CouponCell,
  CouponList,
  GoodsAction,
  GoodsActionIcon,
  GoodsActionButton,
  SubmitBar,
  Sku,
  
  // 组合式 API
  showToast,
  showDialog,
  showNotify,
  showImagePreview,
  showLoadingToast,
  showSuccessToast,
  showFailToast,
  closeToast,
  
  // 配置组件
  ConfigProvider,
  
  // 其他常用组件
  BackTop,
  Calendar,
  Cascader,
  DatetimePicker,
  Picker,
  PickerGroup,
  Search,
  ShareSheet,
  Signature,
  Space,
  TextEllipsis,
  TimePicker,
  Watermark,
} from 'vant'

// 需要注册的组件列表
const components = [
  Button,
  Cell,
  CellGroup,
  Icon,
  VanImage,
  Row,
  Col,
  Popup,
  Field,
  Form,
  Checkbox,
  CheckboxGroup,
  Radio,
  RadioGroup,
  Switch,
  Stepper,
  Rate,
  Slider,
  Uploader,
  ActionSheet,
  Dialog,
  DropdownMenu,
  DropdownItem,
  Loading,
  Notify,
  Overlay,
  PullRefresh,
  SwipeCell,
  Toast,
  Circle,
  Collapse,
  CollapseItem,
  CountDown,
  Divider,
  Empty,
  ImagePreview,
  Lazyload,
  List,
  NoticeBar,
  Progress,
  Skeleton,
  Steps,
  Step,
  Sticky,
  Swipe,
  SwipeItem,
  Tag,
  Grid,
  GridItem,
  IndexBar,
  IndexAnchor,
  NavBar,
  Pagination,
  Sidebar,
  SidebarItem,
  Tab,
  Tabs,
  Tabbar,
  TabbarItem,
  TreeSelect,
  AddressEdit,
  AddressList,
  Area,
  Card,
  ContactCard,
  ContactEdit,
  ContactList,
  Coupon,
  CouponCell,
  CouponList,
  GoodsAction,
  GoodsActionIcon,
  GoodsActionButton,
  SubmitBar,
  Sku,
  ConfigProvider,
  BackTop,
  Calendar,
  Cascader,
  DatetimePicker,
  Picker,
  PickerGroup,
  Search,
  ShareSheet,
  Signature,
  Space,
  TextEllipsis,
  TimePicker,
  Watermark,
]

// 注册组件的函数
export function setupVant(app: App) {
  components.forEach(component => {
    app.component(component.name, component)
  })
}

// 导出组合式 API 函数，方便在组件中使用
export {
  showToast,
  showDialog,
  showNotify,
  showImagePreview,
  showLoadingToast,
  showSuccessToast,
  showFailToast,
  closeToast,
}

// 默认导出设置函数
export default setupVant
