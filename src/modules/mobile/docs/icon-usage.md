# Mobile端图标尺寸控制指南

## 问题描述
在移动端使用Vant组件时，图标的尺寸无法通过普通的`font-size`属性控制，需要特殊的处理方式。

## 解决方案

### 1. 使用工具类（推荐）

```vue
<template>
  <!-- 小图标 (20px) -->
  <van-icon name="search" class="icon-xs" />
  
  <!-- 中小图标 (28px) -->
  <van-icon name="edit" class="icon-sm" />
  
  <!-- 中等图标 (32px) -->
  <van-icon name="eye-o" class="icon-md" />
  
  <!-- 大图标 (40px) -->
  <van-icon name="wap-nav" class="icon-lg" />
  
  <!-- 超大图标 (48px) -->
  <van-icon name="cross" class="icon-xl" />
  
  <!-- 2倍大图标 (56px) -->
  <van-icon name="arrow-left" class="icon-2xl" />
  
  <!-- 3倍大图标 (64px) -->
  <van-icon name="success" class="icon-3xl" />
</template>
```

### 2. 使用SCSS混合器

```scss
.my-custom-icon {
  @include icon-size(36); // 自定义36px大小
}

.header-icon {
  @include icon-size(32); // Header中的图标32px
}
```

### 3. 直接使用:deep()选择器

```scss
.my-icon-container {
  :deep(.van-icon) {
    font-size: 24px !important;
    width: 24px !important;
    height: 24px !important;
  }
}
```

## 可用的工具类

| 类名 | 尺寸 | 用途 |
|------|------|------|
| `.icon-xs` | 20px | 小按钮、标签图标 |
| `.icon-sm` | 28px | 表单图标、提示图标 |
| `.icon-md` | 32px | 导航图标、操作图标 |
| `.icon-lg` | 40px | 主要操作图标 |
| `.icon-xl` | 48px | 大按钮图标 |
| `.icon-2xl` | 56px | 特大图标 |
| `.icon-3xl` | 64px | 超大图标 |

## 在不同组件中的使用

### 表单字段图标
```vue
<van-field
  v-model="password"
  :type="showPassword ? 'text' : 'password'"
  :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
  class="icon-md"
  @click-right-icon="showPassword = !showPassword"
/>
```

### Header图标
```vue
<van-icon 
  name="search" 
  class="header-icon icon-md"
  @click="handleSearch"
/>
```

### 按钮图标
```vue
<van-button class="icon-sm">
  <van-icon name="plus" />
  添加
</van-button>
```

## 注意事项

1. **使用!important**: 由于Vant组件的CSS优先级较高，需要使用`!important`来覆盖默认样式。

2. **同时设置width和height**: 仅设置`font-size`可能不够，需要同时设置`width`和`height`。

3. **line-height重置**: 设置`line-height: 1`避免图标在容器中的对齐问题。

4. **支持多种图标**: 混合器同时支持Vant图标和自定义SVG图标。

## 自定义尺寸

如果预设的尺寸不满足需求，可以创建自定义类：

```scss
.icon-custom {
  @include icon-size(26); // 自定义26px
}
```

或者直接在组件中使用：

```vue
<style scoped>
.my-special-icon {
  @include icon-size(42);
}
</style>
```
