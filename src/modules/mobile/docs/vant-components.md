# Mobile端 Vant 组件管理指南

## 概述

Mobile端使用按组件导入的方式管理 Vant 组件，所有需要使用的组件都在 `src/modules/mobile/plugins/vant.ts` 文件中统一管理。

## 文件结构

```
src/modules/mobile/
├── plugins/
│   └── vant.ts          # Vant 组件导入和注册
├── main.ts              # 应用入口，调用 Vant 设置
└── docs/
    └── vant-components.md # 本文档
```

## 如何添加新的 Vant 组件

### 1. 在 vant.ts 中添加导入

```typescript
// 在导入区域添加新组件
import {
  // 现有组件...
  Button,
  Cell,
  // 添加新组件
  NewComponent,
} from 'vant'
```

### 2. 在组件列表中注册

```typescript
// 在 components 数组中添加新组件
const components = [
  Button,
  Cell,
  // 添加新组件
  NewComponent,
]
```

### 3. 在组件中使用

```vue
<template>
  <!-- 直接使用，无需额外导入 -->
  <van-new-component />
</template>
```

## 已包含的组件分类

### 基础组件
- Button (按钮)
- Cell (单元格)
- CellGroup (单元格组)
- Icon (图标)
- Image (图片)
- Row/Col (布局)
- Popup (弹出层)

### 表单组件
- Field (输入框)
- Form (表单)
- Checkbox/CheckboxGroup (复选框)
- Radio/RadioGroup (单选框)
- Switch (开关)
- Stepper (步进器)
- Rate (评分)
- Slider (滑块)
- Uploader (文件上传)

### 反馈组件
- ActionSheet (动作面板)
- Dialog (弹窗)
- DropdownMenu (下拉菜单)
- Loading (加载)
- Notify (消息通知)
- Overlay (遮罩层)
- PullRefresh (下拉刷新)
- SwipeCell (滑动单元格)
- Toast (轻提示)

### 展示组件
- Circle (环形进度条)
- Collapse (折叠面板)
- CountDown (倒计时)
- Divider (分割线)
- Empty (空状态)
- ImagePreview (图片预览)
- List (列表)
- NoticeBar (通知栏)
- Progress (进度条)
- Skeleton (骨架屏)
- Steps (步骤条)
- Sticky (粘性定位)
- Swipe (轮播)
- Tag (标签)

### 导航组件
- Grid (宫格)
- IndexBar (索引栏)
- NavBar (导航栏)
- Pagination (分页)
- Sidebar (侧边导航)
- Tab/Tabs (标签页)
- Tabbar (标签栏)
- TreeSelect (分类选择)

### 业务组件
- AddressEdit/AddressList (地址)
- Area (省市区选择)
- Card (商品卡片)
- Contact (联系人)
- Coupon (优惠券)
- GoodsAction (商品导购)
- SubmitBar (提交订单栏)
- Sku (商品规格)

## 组合式 API 使用

```typescript
// 在组件中导入并使用
import { showToast, showDialog } from '@mobile/plugins/vant'

// 使用
showToast('提示信息')
showDialog({
  title: '标题',
  message: '内容'
})
```

## 样式覆盖

自定义样式在 `src/modules/mobile/styles/index.scss` 中定义，确保在 Vant 样式之后加载，可以正确覆盖默认样式。

## 注意事项

1. **添加新组件时**：记得同时在导入和组件数组中添加
2. **移除组件时**：从导入和组件数组中同时移除
3. **样式冲突**：优先使用 CSS 变量覆盖，必要时使用 `!important`
4. **按需加载**：只导入实际使用的组件，保持包体积最小

## 维护建议

- 定期检查并移除未使用的组件
- 按功能分类组织导入顺序
- 添加注释说明特殊用途的组件
- 保持与 Vant 官方文档同步更新
