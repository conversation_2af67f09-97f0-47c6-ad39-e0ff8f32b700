import LayoutMobile from '@mobile/layout/index.vue'

export default {
  path: '/',
  redirect: {
    name: 'home',
  },
  component: LayoutMobile,
  children: [
    {
      path: '',
      name: 'home',
      meta: { title: 'nav.mobile' },
      component: () => import('@mobile/views/index.vue'),
    },
    {
      path: 'login',
      name: 'login',
      meta: { title: 'nav.login' },
      component: () => import('@mobile/views/Login/Login.vue'),
    },
    {
      path: 'register',
      name: 'register',
      meta: { title: 'Register' },
      component: () => import('@mobile/views/Register/Register.vue'),
    },
    {
      path: 'forgot-password',
      name: 'forgot-password',
      meta: { title: 'Forgot Password' },
      component: () => import('@mobile/views/ForgotPassword/ForgotPassword.vue'),
    },
  ],
}
