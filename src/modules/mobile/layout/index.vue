<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { loadVantLocale } from '@/locale/vant'
import { watchEffect } from 'vue'
import Header from '@mobile/components/Header.vue'

const { t, locale } = useI18n()

watchEffect(() => {
  loadVantLocale(locale)
})

const handleSearch = () => {
  console.log('Search clicked')
}

const handleMenu = () => {
  console.log('Menu clicked')
}
</script>

<template>
  <div class="mobile-layout">
    <!-- 统一Header - 始终显示，所有图标都显示 -->
    <Header
      show-back
      show-close
      show-search
      show-menu
      @search="handleSearch"
      @menu="handleMenu"
    />

    <!-- 主要内容区域 -->
    <div class="mobile-content mobile-page-content">
      <RouterView />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mobile-layout {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.mobile-content {
  min-height: 100vh;
}
</style>
