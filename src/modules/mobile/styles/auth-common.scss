// Mobile端认证页面公共样式
// 用于登录、注册、忘记密码页面

// 主容器样式
.auth-container {
  min-height: 100vh;
  padding: 0.427rem; // 16px转换为rem (16/37.5)
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: left;
  .login-title{
    font-weight: bold;
    color: #000000;
    font-size: 0.48rem; // 18px转换为rem (18/37.5)
    margin-bottom: 0.64rem; // 24px转换为rem (24/37.5)
    margin-top: 0.64rem; // 24px转换为rem (24/37.5)
  }
}

// 表单卡片样式
.auth-form {
  border-radius: 0.427rem; // 16px转换为rem (16/37.5)
  width: 100%;
}

// 标题样式
.auth-title {
  font-size: 0.64rem; // 24px转换为rem (24/37.5)
  font-weight: bold;
  color: #323233;
  margin-bottom: 0.213rem; // 8px转换为rem (8/37.5)
  text-align: center;
}

.auth-subtitle {
  font-size: 0.373rem; // 14px转换为rem (14/37.5)
  color: #6b7280;
  line-height: 1.4;
  text-align: center;
  margin-bottom: 0.853rem; // 32px转换为rem (32/37.5)
}

// 按钮组样式
.auth-button-group {
  margin: 0.64rem 0; // 24px转换为rem (24/37.5)

  :deep(.van-button) {
    height: 1.28rem; // 48px转换为rem (48/37.5)
    font-size: 0.427rem; // 16px转换为rem (16/37.5)
    font-weight: 500;
    border-radius: 0.96rem; // 36px转换为rem (36/37.5)
    transition: all 0.4s ease;

    // 主要按钮样式
    &.auth-primary-btn:not(.van-button--disabled) {
      background: #1f2937;
      border-color: #1f2937;
      color: white;

      &:hover {
        background: #111827;
        border-color: #111827;
      }
    }

    // 次要按钮样式 (如保存按钮)
    &.auth-secondary-btn:not(.van-button--disabled) {
      background: #9ca3af;
      border-color: #9ca3af;
      color: white;

      &:hover {
        background: #6b7280;
        border-color: #6b7280;
      }
    }

    // 禁用状态
    &.van-button--disabled {
      background: #9ca3af;
      border-color: #9ca3af;
      color: white;
      cursor: not-allowed;
    }
  }
}

// 表单字段样式
:deep(.van-cell-group) {
  margin-bottom: 0.427rem; // 16px转换为rem (16/37.5)
  border-radius: 0.213rem; // 8px转换为rem (8/37.5)
  overflow: hidden;

  .van-cell {
    background: white;
    padding: 0.32rem 0.427rem; // 12px 16px转换为rem

    &::after {
      border-color: #e5e7eb;
    }
  }

  .van-field__label {
    color: #374151;
    font-weight: 500;
    font-size: 0.373rem; // 14px转换为rem (14/37.5)
    margin-bottom: 0.213rem; // 8px转换为rem (8/37.5)
  }

  .van-field__control {
    color: #374151;
    font-size: 0.427rem; // 16px转换为rem (16/37.5)

    &::placeholder {
      color: #9ca3af;
    }
  }

  .van-field__right-icon {
    font-size: 0.533rem !important; // 20px转换为rem (20/37.5)
    color: #9ca3af;

    // 强制控制Vant图标大小
    :deep(.van-icon) {
      font-size: 0.533rem !important; // 20px转换为rem (20/37.5)
      width: 0.533rem !important; // 20px转换为rem (20/37.5)
      height: 0.533rem !important; // 20px转换为rem (20/37.5)
    }
  }

  // 验证码按钮样式
  .van-field__button {
    :deep(.van-button) {
      height: 0.853rem; // 32px转换为rem (32/37.5)
      font-size: 0.32rem; // 12px转换为rem (12/37.5)
      border-radius: 0.213rem; // 8px转换为rem (8/37.5)
      background: #059669;
      border-color: #059669;
      color: white;

      &:hover {
        background: #047857;
        border-color: #047857;
      }

      &.van-button--disabled {
        background: #9ca3af;
        border-color: #9ca3af;
      }
    }
  }

  // 错误状态
  .van-field--error {
    .van-field__control {
      color: #ef4444;
    }

    .van-field__error-message {
      color: #ef4444;
      font-size: 0.32rem; // 12px转换为rem (12/37.5)
      margin-top: 0.107rem; // 4px转换为rem (4/37.5)
    }
  }
}

// 协议文本样式
.auth-agreement-text {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  line-height: 1.5;
  margin-bottom:32px;

  .link {
    color: #059669;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 分割线样式
:deep(.van-divider) {
  color: #6b7280;
  border-color: #e5e7eb;
  font-size: 14px;
  font-weight: 500;
  margin: 32px 0;

  &::before,
  &::after {
    border-color: #e5e7eb;
  }
}

// 社交登录样式
.auth-social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;

  :deep(.van-button) {
    height: 44px;
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }

    &.apple-btn {
      background: #000000;
      border-color: #000000;
      color: white;

      &:hover {
        background: #1f2937;
        border-color: #1f2937;
      }
    }
  }
}

// 链接样式
.auth-link {
  text-align: center;
  font-size: 14px;
  color: #6b7280;

  span {
    margin-right: 8px;
  }

  :deep(.van-button) {
    font-size: 14px;
    color: #059669;

    &:hover {
      color: #047857;
    }
  }

  .link {
    color: #059669;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
      color: #047857;
    }
  }
}

// 提示文本样式
.auth-requirements {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 16px;
  padding: 0 32px;
}

// 特殊样式 - 忘记密码页面的额外样式
.forgot-password-header {
  :deep(.van-nav-bar) {
    background: white;
    height: 56px;

    .van-nav-bar__title {
      color: #323233;
      font-weight: 600;
      font-size: 18px;
    }

    .van-nav-bar__text {
      color: #059669;
      font-size: 16px;
    }

    .van-nav-bar__arrow {
      font-size: 16px;
    }
  }
}

.forgot-password-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.forgot-password-title-section {
  text-align: center;
  margin-bottom: 32px;
  max-width: 800px;
}

// 图标尺寸控制工具类
.icon-xs {
  :deep(.van-icon) {
    font-size: 10px !important;
    width: 10px !important;
    height: 10px !important;
  }
}

.icon-sm {
  :deep(.van-icon) {
    font-size: 14px !important;
    width:14px !important;
    height: 14px !important;
  }
}

.icon-md {
  :deep(.van-icon) {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
  }
}

.icon-lg {
  :deep(.van-icon) {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
}

.icon-xl {
  :deep(.van-icon) {
    font-size: 24px !important;
    width: 24px !important;
    height: 24px !important;
  }
}

// 全局Vant图标样式重置
:deep(.van-icon) {
  // 确保图标可以被自定义尺寸覆盖
  font-size: inherit !important;

  // 移除默认的行高，避免影响布局
  line-height: 1 !important;

  // 确保图标垂直居中
  vertical-align: middle !important;
}
