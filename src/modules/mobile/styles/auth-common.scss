// Mobile端认证页面公共样式
// 用于登录、注册、忘记密码页面

// 主容器样式
.auth-container {
  min-height: 100vh;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 表单卡片样式
.auth-form {
  border-radius: 32px;
  padding: 96px;
  width: 100%;
  max-width: 800px;
}

// 标题样式
.auth-title {
  font-size: 48px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
  text-align: center;
}

.auth-subtitle {
  font-size: 28px;
  color: #6b7280;
  line-height: 1.4;
  text-align: center;
  margin-bottom: 64px;
}

// 按钮组样式
.auth-button-group {
  margin: 48px 0;

  :deep(.van-button) {
    height: 96px;
    font-size: 32px;
    font-weight: 500;
    border-radius: 72px;
    transition: all 0.4s ease;

    // 主要按钮样式
    &.auth-primary-btn:not(.van-button--disabled) {
      background: #1f2937;
      border-color: #1f2937;
      color: white;

      &:hover {
        background: #111827;
        border-color: #111827;
      }
    }

    // 次要按钮样式 (如保存按钮)
    &.auth-secondary-btn:not(.van-button--disabled) {
      background: #9ca3af;
      border-color: #9ca3af;
      color: white;

      &:hover {
        background: #6b7280;
        border-color: #6b7280;
      }
    }

    // 禁用状态
    &.van-button--disabled {
      background: #9ca3af;
      border-color: #9ca3af;
      color: white;
      cursor: not-allowed;
    }
  }
}

// 表单字段样式
:deep(.van-cell-group) {
  margin-bottom: 32px;
  border-radius: 16px;
  overflow: hidden;

  .van-cell {
    background: white;
    padding: 24px 32px;

    &::after {
      border-color: #e5e7eb;
    }
  }

  .van-field__label {
    color: #374151;
    font-weight: 500;
    font-size: 28px;
    margin-bottom: 16px;
  }

  .van-field__control {
    color: #374151;
    font-size: 32px;

    &::placeholder {
      color: #9ca3af;
    }
  }

  .van-field__right-icon {
    font-size: 40px !important;
    color: #9ca3af;

    // 强制控制Vant图标大小
    :deep(.van-icon) {
      font-size: 40px !important;
      width: 40px !important;
      height: 40px !important;
    }
  }

  // 验证码按钮样式
  .van-field__button {
    :deep(.van-button) {
      height: 64px;
      font-size: 24px;
      border-radius: 16px;
      background: #059669;
      border-color: #059669;
      color: white;

      &:hover {
        background: #047857;
        border-color: #047857;
      }

      &.van-button--disabled {
        background: #9ca3af;
        border-color: #9ca3af;
      }
    }
  }

  // 错误状态
  .van-field--error {
    .van-field__control {
      color: #ef4444;
    }

    .van-field__error-message {
      color: #ef4444;
      font-size: 24px;
      margin-top: 8px;
    }
  }
}

// 协议文本样式
.auth-agreement-text {
  font-size: 24px;
  color: #6b7280;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 64px;

  .link {
    color: #059669;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// 分割线样式
:deep(.van-divider) {
  color: #6b7280;
  border-color: #e5e7eb;
  font-size: 28px;
  font-weight: 500;
  margin: 64px 0;

  &::before,
  &::after {
    border-color: #e5e7eb;
  }
}

// 社交登录样式
.auth-social-login {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 48px;

  :deep(.van-button) {
    height: 88px;
    background: white;
    color: #374151;
    border: 2px solid #d1d5db;
    border-radius: 16px;
    font-size: 28px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }

    &.apple-btn {
      background: #000000;
      border-color: #000000;
      color: white;

      &:hover {
        background: #1f2937;
        border-color: #1f2937;
      }
    }
  }
}

// 链接样式
.auth-link {
  text-align: center;
  font-size: 28px;
  color: #6b7280;

  span {
    margin-right: 16px;
  }

  :deep(.van-button) {
    font-size: 28px;
    color: #059669;

    &:hover {
      color: #047857;
    }
  }

  .link {
    color: #059669;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
      color: #047857;
    }
  }
}

// 提示文本样式
.auth-requirements {
  font-size: 24px;
  color: #6b7280;
  text-align: center;
  line-height: 1.4;
  margin-bottom: 32px;
  padding: 0 32px;
}

// 特殊样式 - 忘记密码页面的额外样式
.forgot-password-header {
  :deep(.van-nav-bar) {
    background: white;
    height: 112px;

    .van-nav-bar__title {
      color: #323233;
      font-weight: 600;
      font-size: 36px;
    }

    .van-nav-bar__text {
      color: #059669;
      font-size: 32px;
    }

    .van-nav-bar__arrow {
      font-size: 32px;
    }
  }
}

.forgot-password-content {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.forgot-password-title-section {
  text-align: center;
  margin-bottom: 64px;
  max-width: 800px;
}

// 图标尺寸控制工具类
.icon-xs {
  :deep(.van-icon) {
    font-size: 20px !important;
    width: 20px !important;
    height: 20px !important;
  }
}

.icon-sm {
  :deep(.van-icon) {
    font-size: 28px !important;
    width: 28px !important;
    height: 28px !important;
  }
}

.icon-md {
  :deep(.van-icon) {
    font-size: 32px !important;
    width: 32px !important;
    height: 32px !important;
  }
}

.icon-lg {
  :deep(.van-icon) {
    font-size: 40px !important;
    width: 40px !important;
    height: 40px !important;
  }
}

.icon-xl {
  :deep(.van-icon) {
    font-size: 48px !important;
    width: 48px !important;
    height: 48px !important;
  }
}

// 全局Vant图标样式重置
:deep(.van-icon) {
  // 确保图标可以被自定义尺寸覆盖
  font-size: inherit !important;

  // 移除默认的行高，避免影响布局
  line-height: 1 !important;

  // 确保图标垂直居中
  vertical-align: middle !important;
}
