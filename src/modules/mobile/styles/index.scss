// 导入认证页面公共样式
@import './auth-common.scss';
:root{
  --base-color:#00C65E;
  --van-primary-color: var(--base-color);
  --bg-color:var(-base-bg-color);
  --van-cell-vertical-padding:0;
}

body {
  min-width: 320px;
}
.van-cell{
  display: block!important;
}
.van-button--primary{
  background:var(--base-color)!important;
  border-color:var(--base-color)!important;
}
.van-button--disabled{
    background: #CCCCCC!important;
  border-color: #CCCCCC!important;
}
.van-cell-group--inset{
  margin:0!important;
}
.el-icon{
  --color: inherit;
  align-items: center;
  display: inline-flex
;
  height: 1em;
  justify-content: center;
  line-height: 1em;
  position: relative;
  width: 1em;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  svg{
    height: 1em;
    width: 1em;
    vertical-align: bottom;
  }
}
.van-cell,.van-cell-group{
  background: transparent!important;
  .van-field__label{
    font-weight: bold;
  }
  .van-cell__value{
    background: white;
    height:56px;
    line-height:56px;
    border-radius: 12px;
    padding:0 16px;
  }
}
//.van-button__text{
//  .el-icon{
//    font-size: 32px;
//  }
//}