// 导入认证页面公共样式
@import './auth-common.scss';

// 全局CSS变量定义 - 确保能够覆盖Vant默认样式
:root {
  --base-color: #00C65E;
  --base-bg-color: #f5f5f5;
  --bg-color: var(--base-bg-color);

  // Vant 组件相关变量 - 使用正确的变量名
  --van-primary-color: var(--base-color);
  --van-button-primary-background-color: var(--base-color);
  --van-button-primary-border-color: var(--base-color);
  --van-button-large-height: 1.493rem; // 56px转换为rem (56/37.5)
  --van-cell-vertical-padding: 0;
  --van-button-disabled-background-color: #CCCCCC;
  --van-button-disabled-border-color: #CCCCCC;
}

// 使用更强的选择器确保样式优先级
html:root {
  --van-primary-color: var(--base-color);
  --van-button-primary-background-color: var(--base-color);
  --van-button-primary-border-color: var(--base-color);
}

body {
  min-width: 8.533rem; // 320px转换为rem (320/37.5)
}

// Vant组件样式覆盖 - 使用更强的选择器优先级
.van-cell {
  display: block !important;
}

.van-button--primary {
  background: var(--base-color) !important;
  border-color: var(--base-color) !important;
}

.van-button--disabled {
  background: #CCCCCC !important;
  border-color: #CCCCCC !important;
}

.van-cell-group--inset {
  margin: 0 !important;
}
.el-icon{
  --color: inherit;
  align-items: center;
  display: inline-flex
;
  height: 1em;
  justify-content: center;
  line-height: 1em;
  position: relative;
  width: 1em;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  svg{
    height: 1em;
    width: 1em;
    vertical-align: bottom;
  }
}
.van-cell,
.van-cell-group {
  background: transparent !important;

  .van-field__label {
    font-weight: bold;
  }

  .van-cell__value {
    background: white;
    height: 1.493rem; // 56px转换为rem (56/37.5)
    line-height: 1.493rem; // 56px转换为rem (56/37.5)
    border-radius: 0.32rem; // 12px转换为rem (12/37.5)
    padding: 0 0.427rem; // 16px转换为rem (16/37.5)
  }
}
//.van-button__text{
//  .el-icon{
//    font-size: 32px;
//  }
//}