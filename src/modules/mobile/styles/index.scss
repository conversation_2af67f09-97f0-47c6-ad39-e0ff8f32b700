// 导入认证页面公共样式
@import './auth-common.scss';
:root{
  --base-color:#00C65E;
  --van-primary-color: var(--base-color);
  --bg-color:var(-base-bg-color);
  --van-cell-vertical-padding:0;
  --van-button-large-height:56px;
  --van-cell-horizontal-padding:0;
  --van-divider-font-size:12px;
  --van-border-color:#CCCCCC;
  --van-padding-base:9px;
  --van-field-label-width:100%;
}

body {
  min-width: 320px;
}
.van-cell{
  display: block!important;
}
.van-button--primary{
  background:var(--base-color)!important;
  border-color:var(--base-color)!important;
}
.van-button--disabled{
    background: #CCCCCC!important;
  border-color: #CCCCCC!important;
}
.van-cell-group--inset{
  margin:0!important;
  .van-cell{
    &:not(:last-child){
      margin-bottom: 16px;
    }
  }
}
.el-icon{
  --color: inherit;
  align-items: center;
  display: inline-flex
;
  height: 16px;
  vertical-align: middle;
  justify-content: center;
  line-height:  16px;
  position: relative;
  width:  16px;
  fill: currentColor;
  color: var(--color);
  font-size: inherit;
  svg{
    height:  16px;
    width:  16px;
    vertical-align: bottom;
  }
}
.van-cell,.van-cell-group{
  background: transparent;
  .van-field__label{
    font-weight: bold;
    display: flex
  ;
    justify-content: space-between;
  }
  .van-cell__value{
    background: white;
    height:56px;
    line-height:56px;
    border-radius: 12px;
    padding:0 16px;
  }
}
//.van-button__text{
//  .el-icon{
//    font-size: 32px;
//  }
//}