<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

interface Props {
  title?: string
  showBack?: boolean
  showClose?: boolean
  showSearch?: boolean
  showMenu?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: 'GlocalMe',
  showBack: false,
  showClose: false,
  showSearch: true,
  showMenu: true
})

const emit = defineEmits<{
  back: []
  close: []
  search: []
  menu: []
}>()

const router = useRouter()

const handleBack = () => {
  if (props.showBack) {
    emit('back')
    router.back()
  }
}

const handleClose = () => {
  emit('close')
}

const handleSearch = () => {
  emit('search')
}

const handleMenu = () => {
  emit('menu')
}
</script>

<template>
  <div class="mobile-header">
    <!-- 主要Header -->
    <div class="header-main">
      <!-- 左侧 -->
      <div class="header-left">
        <img src="@/assets/images/Logo.svg" alt="">
      </div>
      <!-- 右侧 -->
      <div class="header-right">
        <van-icon 
          v-if="showSearch" 
          name="search" 
          class="header-icon search-icon"
          @click="handleSearch"
        />
        <van-icon 
          v-if="showMenu" 
          name="wap-nav" 
          class="header-icon menu-icon"
          @click="handleMenu"
        />
      </div>
    </div>
    <!-- 下载提示条 -->
    <div class="download-banner">
      <span class="download-text">Download GlocalMe App on the App Store / Google Play</span>
      <van-icon name="cross" class="close-icon" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 116px;
  background: white;
  .header-left{
    img{
      height: 20px;
    }
  }
}

.download-banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
   line-height: 44px;
  padding:0 16px;
  background:var(--base-bg-color);
  font-size: 12px;
  height:44px;

  .download-text {
    flex: 1;
    text-align: left;
    font-weight: 500;
    color: var(--base-color);
  }

  .close-icon {
    font-size: 14px;
    color:#000000;
    cursor: pointer;
    padding: 4px;

    &:hover {
      opacity: 0.8;
    }
  }
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  height: 72px;
  background: white;
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left {
  justify-content: flex-start;
}

.header-right {
  justify-content: flex-end;
}
.header-icon {
  font-size: 20px;
  color: #323233;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;

}

.search-icon,
.menu-icon {
  width: 36px;
  height: 36px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color:var(--base-color);
  }
}

// 为页面内容添加顶部间距，避免被固定header遮挡
:global(.mobile-page-content) {
  padding-top: 48px;
}

// 如果没有下载横幅时的间距
:global(.mobile-page-content--no-banner) {
  padding-top: 20px; // header-main(112px) + 16px
}
</style>
