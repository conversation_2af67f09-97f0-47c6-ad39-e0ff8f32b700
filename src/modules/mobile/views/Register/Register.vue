<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'

const { t } = useI18n()
const router = useRouter()

const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  register,
  resetForm
} = useAuth()

// 计算属性
const canContinue = computed(() => {
  return emailValid.value && passwordValid.value && passwordsMatch.value
})

// 处理注册
const handleRegister = async () => {
  if (!canContinue.value) return
  
  showLoadingToast({
    message: '注册中...',
    forbidClick: true,
  })
  
  const result = await register(email.value, password.value)
  closeToast()
  
  if (result.success) {
    showToast(result.message)
    resetForm()
    router.push('/')
  } else {
    showToast(result.message)
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  }
}

// 社交注册
const handleSocialRegister = async (provider: 'google' | 'apple') => {
  showLoadingToast({
    message: '注册中...',
    forbidClick: true,
  })
  
  setTimeout(() => {
    closeToast()
    showToast(`${provider} 注册功能开发中`)
  }, 1000)
}

// 跳转登录
const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="register-container">
    <!-- 移除了register-header，因为现在使用统一的Header组件 -->

    <div class="register-form">
      <!-- 表单字段 -->
      <van-cell-group inset>
        <van-field
          v-model="email"
          :label="t('login.email_label')"
          :placeholder="t('login.email_placeholder')"
          type="email"
          :error="email && !emailValid"
          error-message="请输入正确的邮箱地址"
        />
        
        <van-field
          v-model="password"
          label="密码"
          :type="showPassword ? 'text' : 'password'"
          placeholder="请输入密码"
          :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
          :error="password && !passwordValid"
          error-message="密码必须6-20位，包含字母、数字和符号"
          @click-right-icon="showPassword = !showPassword"
        />
        
        <van-field
          v-model="retypePassword"
          label="确认密码"
          :type="showRetypePassword ? 'text' : 'password'"
          placeholder="请再次输入密码"
          :right-icon="showRetypePassword ? 'eye-o' : 'closed-eye'"
          :error="retypePassword && !passwordsMatch"
          :error-message="passwordError"
          @click-right-icon="showRetypePassword = !showRetypePassword"
        />
      </van-cell-group>

      <!-- 注册按钮 -->
      <div class="button-group">
        <van-button
          type="primary"
          size="large"
          block
          round
          :loading="isLoading"
          :disabled="!canContinue"
          @click="handleRegister"
        >
          注册
        </van-button>
      </div>

      <!-- 密码要求提示 -->
      <div class="password-requirements">
        密码必须6-20位，包含字母、数字和符号中的至少两种。
      </div>

      <!-- 协议文本 -->
      <div class="agreement-text">
        {{ t('login.agreement_text') }}
        <span class="link">{{ t('login.user_agreement') }}</span>
        {{ t('login.and') }}
        <span class="link">{{ t('login.privacy_policy') }}</span>
      </div>

      <!-- 分割线 -->
      <van-divider>{{ t('login.or') }}</van-divider>

      <!-- 社交注册 -->
      <div class="social-register">
        <van-button
          size="large"
          block
          round
          icon="https://img.yzcdn.cn/vant/logo.png"
          @click="handleSocialRegister('google')"
        >
          {{ t('login.continue_with_google') }}
        </van-button>
        
        <van-button
          size="large"
          block
          round
          class="apple-btn"
          @click="handleSocialRegister('apple')"
        >
          {{ t('login.continue_with_apple') }}
        </van-button>
      </div>

      <!-- 登录链接 -->
      <div class="login-link">
        <span>已有账户？</span>
        <van-button type="primary" size="mini" plain @click="goToLogin">
          立即登录
        </van-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.register-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.register-form {
  flex: 1;
}

.button-group {
  margin: 24px 0;
  
  :deep(.van-button) {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    
    &.van-button--disabled {
      background-color: #c8c9cc;
      border-color: #c8c9cc;
    }
  }
}

.password-requirements {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.4;
  margin-bottom: 16px;
  padding: 0 16px;
}

.agreement-text {
  font-size: 12px;
  color: #969799;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 24px;
  
  .link {
    color: #1989fa;
  }
}

.social-register {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
  
  :deep(.van-button) {
    height: 44px;
    background: white;
    color: #323233;
    border: 1px solid #ebedf0;
    
    &.apple-btn {
      background: #000;
      color: white;
      border-color: #000;
    }
  }
}

.login-link {
  text-align: center;
  font-size: 14px;
  color: #969799;
  
  span {
    margin-right: 8px;
  }
}

:deep(.van-cell-group) {
  margin-bottom: 16px;
  
  .van-cell {
    background: rgba(255, 255, 255, 0.95);
    
    &::after {
      border-color: rgba(0, 0, 0, 0.1);
    }
  }
  
  .van-field__label {
    color: #323233;
    font-weight: 500;
  }
  
  .van-field__control {
    color: #323233;
    
    &::placeholder {
      color: #c8c9cc;
    }
  }
  
  .van-field--error {
    .van-field__control {
      color: #ee0a24;
    }
  }
}

:deep(.van-divider) {
  color: #969799;
  border-color: rgba(255, 255, 255, 0.3);
  
  &::before,
  &::after {
    border-color: rgba(255, 255, 255, 0.3);
  }
}
</style>
