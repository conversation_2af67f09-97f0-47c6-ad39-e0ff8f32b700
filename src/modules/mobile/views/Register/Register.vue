<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'

const { t } = useI18n()
const router = useRouter()

const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  register,
  resetForm
} = useAuth()

// 计算属性
const canContinue = computed(() => {
  return emailValid.value && passwordValid.value && passwordsMatch.value
})

// 处理注册
const handleRegister = async () => {
  if (!canContinue.value) return

  showLoadingToast({
    message: '注册中...',
    forbidClick: true,
  })

  const result = await register(email.value, password.value)
  closeToast()

  if (result.success) {
    showToast(result.message)
    resetForm()
    router.push('/')
  } else {
    showToast(result.message)
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  }
}

// 社交注册
const handleSocialRegister = async (provider: 'google' | 'apple') => {
  showLoadingToast({
    message: '注册中...',
    forbidClick: true,
  })

  setTimeout(() => {
    closeToast()
    showToast(`${provider} 注册功能开发中`)
  }, 1000)
}

// 跳转登录
const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="auth-container">
    <div class="auth-form">
      <!-- 表单字段 -->
      <van-cell-group inset>
        <van-field
          v-model="email"
          :label="t('login.email_label')"
          :placeholder="t('login.email_placeholder')"
          type="email"
          :error="email && !emailValid"
          error-message="请输入正确的邮箱地址"
        />

        <van-field
          v-model="password"
          label="密码"
          :type="showPassword ? 'text' : 'password'"
          placeholder="请输入密码"
          :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
          :error="password && !passwordValid"
          error-message="密码必须6-20位，包含字母、数字和符号"
          @click-right-icon="showPassword = !showPassword"
        />

        <van-field
          v-model="retypePassword"
          label="确认密码"
          :type="showRetypePassword ? 'text' : 'password'"
          placeholder="请再次输入密码"
          :right-icon="showRetypePassword ? 'eye-o' : 'closed-eye'"
          :error="retypePassword && !passwordsMatch"
          :error-message="passwordError"
          @click-right-icon="showRetypePassword = !showRetypePassword"
        />
      </van-cell-group>

      <!-- 注册按钮 -->
      <div class="auth-button-group">
        <van-button
          type="primary"
          size="large"
          block
          round
          class="auth-primary-btn"
          :loading="isLoading"
          :disabled="!canContinue"
          @click="handleRegister"
        >
          注册
        </van-button>
      </div>

      <!-- 密码要求提示 -->
      <div class="auth-requirements">
        密码必须6-20位，包含字母、数字和符号中的至少两种。
      </div>

      <!-- 协议文本 -->
      <div class="auth-agreement-text">
        {{ t('login.agreement_text') }}
        <span class="link">{{ t('login.user_agreement') }}</span>
        {{ t('login.and') }}
        <span class="link">{{ t('login.privacy_policy') }}</span>
      </div>

      <!-- 分割线 -->
      <van-divider>{{ t('login.or') }}</van-divider>

      <!-- 社交注册 -->
      <div class="auth-social-login">
        <van-button
          size="large"
          block
          round
          icon="https://img.yzcdn.cn/vant/logo.png"
          @click="handleSocialRegister('google')"
        >
          {{ t('login.continue_with_google') }}
        </van-button>

        <van-button
          size="large"
          block
          round
          class="apple-btn"
          @click="handleSocialRegister('apple')"
        >
          {{ t('login.continue_with_apple') }}
        </van-button>
      </div>

      <!-- 登录链接 -->
      <div class="auth-link">
        <span>已有账户？</span>
        <van-button type="primary" size="mini" plain @click="goToLogin">
          立即登录
        </van-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// 注册页面特有样式（如果有的话）
// 所有通用样式已移至公共样式文件 auth-common.scss
</style>
