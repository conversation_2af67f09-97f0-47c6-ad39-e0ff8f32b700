<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'

const { t } = useI18n()
const router = useRouter()

const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  register,
  resetForm
} = useAuth()

// 计算属性
const canContinue = computed(() => {
  return emailValid.value && passwordValid.value && passwordsMatch.value
})

// 处理注册
const handleRegister = async () => {
  if (!canContinue.value) return

  showLoadingToast({
    message: t('register.registering'),
    forbidClick: true,
  })

  const result = await register(email.value, password.value)
  closeToast()

  if (result.success) {
    showToast(result.message)
    resetForm()
    router.push('/')
  } else {
    showToast(result.message)
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  }
}

// 社交注册
const handleSocialRegister = async (provider: 'google' | 'apple') => {
  showLoadingToast({
    message: t('register.registering'),
    forbidClick: true,
  })

  setTimeout(() => {
    closeToast()
    const message = provider === 'google'
      ? t('register.google_register_developing')
      : t('register.apple_register_developing')
    showToast(message)
  }, 1000)
}

// 跳转登录
const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="auth-container">
    <h1 class="login-title">{{ t('register.title') }}</h1>
    <div class="auth-form">
      <!-- 表单字段 -->
      <van-cell-group inset>
        <van-field
          v-model="email"
          :label="t('login.email_label')"
          :placeholder="t('login.email_placeholder')"
          type="email"
          :error-message="t('register.email_error')"
        />

        <van-field
          v-model="password"
          :label="t('register.password_label')"
          :type="showPassword ? 'text' : 'password'"
          :placeholder="t('register.password_placeholder')"
          :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
          :error-message="t('register.password_error')"
          @click-right-icon="showPassword = !showPassword"
        />

        <van-field
          v-model="retypePassword"
          :label="t('register.confirm_password_label')"
          :type="showRetypePassword ? 'text' : 'password'"
          :placeholder="t('register.confirm_password_placeholder')"
          :right-icon="showRetypePassword ? 'eye-o' : 'closed-eye'"
          :error-message="t('register.password_mismatch')"
          @click-right-icon="showRetypePassword = !showRetypePassword"
        />
      </van-cell-group>

      <!-- 注册按钮 -->
      <div class="auth-button-group">
        <van-button
          type="primary"
          size="large"
          block
          round
          class="auth-primary-btn"
          :loading="isLoading"
          :disabled="!canContinue"
          @click="handleRegister"
        >
         {{ t('register.register_button') }}
        </van-button>
      </div>
    </div>
  </div>

</template>

<style scoped lang="scss">
// 注册页面特有样式（如果有的话）
// 所有通用样式已移至公共样式文件 auth-common.scss
</style>
