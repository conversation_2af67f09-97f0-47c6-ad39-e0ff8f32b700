<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuth } from '@/hooks/useAuth'
import { showToast, showLoadingToast, closeToast } from 'vant'

const { t } = useI18n()
const router = useRouter()

const {
  email,
  password,
  retypePassword,
  verificationCode,
  isLoading,
  showPassword,
  showRetypePassword,
  codeSent,
  countdown,
  emailValid,
  passwordValid,
  passwordsMatch,
  codeValid,
  passwordError,
  sendVerificationCode,
  resetPassword,
  resetForm
} = useAuth()

// 计算属性
const canSendCode = computed(() => {
  return emailValid.value && countdown.value === 0
})

const canSave = computed(() => {
  return emailValid.value && codeValid.value && passwordValid.value && passwordsMatch.value
})

// 发送验证码
const handleSendCode = async () => {
  if (!canSendCode.value) return

  showLoadingToast({
    message: '发送中...',
    forbidClick: true,
  })

  const result = await sendVerificationCode(email.value)
  closeToast()

  if (result.success) {
    showToast(result.message)
  } else {
    showToast(result.message)
  }
}

// 保存新密码
const handleSave = async () => {
  if (!canSave.value) return

  showLoadingToast({
    message: '重置中...',
    forbidClick: true,
  })

  const result = await resetPassword(email.value, verificationCode.value, password.value)
  closeToast()

  if (result.success) {
    showToast(result.message)
    resetForm()
    router.push('/login')
  } else {
    showToast(result.message)
  }
}

// 返回登录
const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="forgot-password-container">
    <div class="forgot-password-header">
      <van-nav-bar
        title="忘记密码"
        left-text="返回"
        left-arrow
        @click-left="goToLogin"
      />
    </div>

    <div class="forgot-password-content">
      <div class="forgot-password-title-section">
        <h2 class="auth-title">重置密码</h2>
        <p class="auth-subtitle">我们将发送验证码到您的邮箱，请输入6位验证码</p>
      </div>

      <div class="auth-form">
        <!-- 邮箱输入 -->
        <van-cell-group inset>
          <van-field
            v-model="email"
            label="邮箱"
            placeholder="<EMAIL>"
            type="email"
            :readonly="codeSent"
            :error="email && !emailValid"
            error-message="请输入正确的邮箱地址"
          />
        </van-cell-group>

        <!-- 验证码输入 -->
        <van-cell-group inset>
          <van-field
            v-model="verificationCode"
            label="验证码"
            placeholder="请输入6位验证码"
            maxlength="6"
            :error="verificationCode && !codeValid"
            error-message="请输入6位数字验证码"
          >
            <template #button>
              <van-button
                size="small"
                type="primary"
                :disabled="!canSendCode"
                :loading="isLoading && !codeSent"
                @click="handleSendCode"
              >
                {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
              </van-button>
            </template>
          </van-field>
        </van-cell-group>

        <!-- 新密码输入 -->
        <van-cell-group inset>
          <van-field
            v-model="password"
            label="新密码"
            :type="showPassword ? 'text' : 'password'"
            placeholder="请输入新密码"
            :right-icon="showPassword ? 'eye-o' : 'closed-eye'"
            :error="password && !passwordValid"
            error-message="密码必须6-20位，包含字母、数字和符号"
            @click-right-icon="showPassword = !showPassword"
          />

          <van-field
            v-model="retypePassword"
            label="确认密码"
            :type="showRetypePassword ? 'text' : 'password'"
            placeholder="请再次输入新密码"
            :right-icon="showRetypePassword ? 'eye-o' : 'closed-eye'"
            :error="retypePassword && !passwordsMatch"
            :error-message="passwordError"
            @click-right-icon="showRetypePassword = !showRetypePassword"
          />
        </van-cell-group>

        <!-- 保存按钮 -->
        <div class="auth-button-group">
          <van-button
            type="primary"
            size="large"
            block
            round
            class="auth-secondary-btn"
            :loading="isLoading && codeSent"
            :disabled="!canSave"
            @click="handleSave"
          >
            保存
          </van-button>
        </div>

        <!-- 密码要求提示 -->
        <div class="auth-requirements">
          密码必须6-20位，包含字母、数字和符号中的至少两种。不能是您之前使用过的密码。
        </div>

        <!-- 返回登录链接 -->
        <div class="auth-link">
          <span>想起密码了？</span>
          <van-button type="primary" size="mini" plain @click="goToLogin">
            返回登录
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.forgot-password-container {
  min-height: 100vh;
  background: #f9fafb;
  display: flex;
  flex-direction: column;
}

.forgot-password-header {
  :deep(.van-nav-bar) {
    background: white;
    height: 112px;

    .van-nav-bar__title {
      color: #323233;
      font-weight: 600;
      font-size: 36px;
    }

    .van-nav-bar__text {
      color: #059669;
      font-size: 32px;
    }

    .van-nav-bar__arrow {
      font-size: 32px;
    }
  }
}

.forgot-password-content {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 所有通用样式已移至公共样式文件 auth-common.scss
</style>
