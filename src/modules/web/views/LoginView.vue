<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const router = useRouter()

const email = ref('')
const isLoading = ref(false)

const handleContinue = () => {
  if (!email.value) {
    ElMessage.warning('Please enter your email address')
    return
  }
  
  isLoading.value = true
  // 模拟登录处理
  setTimeout(() => {
    isLoading.value = false
    ElMessage.success('Login successful!')
    router.push('/')
  }, 1000)
}

const handleGoogleLogin = () => {
  console.log('Google login')
}

const handleAppleLogin = () => {
  console.log('Apple login')
}

const handleMobileLogin = () => {
  console.log('Mobile login')
}
</script>

<template>
  <div class="login-container">
    <div class="login-form">
      <h1 class="login-title">{{ t('login.title') }}</h1>
      
      <div class="form-group">
        <label class="form-label">{{ t('login.email_label') }}</label>
        <el-input
          v-model="email"
          :placeholder="t('login.email_placeholder')"
          class="email-input"
          size="large"
          type="email"
        />
      </div>
      
      <el-button
        type="primary"
        class="continue-btn"
        size="large"
        :loading="isLoading"
        @click="handleContinue"
      >
        {{ t('login.continue') }}
      </el-button>
      
      <div class="agreement-text">
        {{ t('login.agreement_text') }}
        <a href="#" class="link">{{ t('login.user_agreement') }}</a>
        {{ t('login.and') }}
        <a href="#" class="link">{{ t('login.privacy_policy') }}</a>
      </div>
      
      <div class="divider">
        <span class="divider-text">{{ t('login.or') }}</span>
      </div>
      
      <div class="social-login">
        <el-button
          class="social-btn google-btn"
          size="large"
          @click="handleGoogleLogin"
        >
          <el-icon class="social-icon">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
          </el-icon>
          {{ t('login.continue_with_google') }}
        </el-button>
        
        <el-button
          class="social-btn apple-btn"
          size="large"
          @click="handleAppleLogin"
        >
          <el-icon class="social-icon">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path fill="currentColor" d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
            </svg>
          </el-icon>
          {{ t('login.continue_with_apple') }}
        </el-button>
        
        <el-button
          class="social-btn mobile-btn"
          size="large"
          @click="handleMobileLogin"
        >
          <el-icon class="social-icon">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path fill="currentColor" d="M17 2H7c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM7 4h10v12H7V4zm5 15c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
            </svg>
          </el-icon>
          {{ t('login.continue_with_mobile') }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-form {
  background: white;
  border-radius: 16px;
  padding: 48px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 32px;
  line-height: 1.3;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

:deep(.email-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;
    
    &:hover {
      border-color: #9ca3af;
    }
    
    &.is-focus {
      border-color: #059669;
      box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }
  }
  
  .el-input__inner {
    font-size: 16px;
    
    &::placeholder {
      color: #9ca3af;
    }
  }
}

.continue-btn {
  width: 100%;
  background: #9ca3af;
  border-color: #9ca3af;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  
  &:hover {
    background: #6b7280;
    border-color: #6b7280;
  }
}

.agreement-text {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 32px;
  
  .link {
    color: #059669;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.divider {
  position: relative;
  text-align: center;
  margin: 32px 0;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
  }
  
  .divider-text {
    background: white;
    color: #6b7280;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 500;
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-btn {
  width: 100%;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  
  .social-icon {
    display: flex;
    align-items: center;
  }
  
  &.google-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;
    
    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }
  
  &.apple-btn {
    background: #000000;
    border: 1px solid #000000;
    color: white;
    
    &:hover {
      background: #1f2937;
      border-color: #1f2937;
    }
  }
  
  &.mobile-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;
    
    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }
}
</style>
