<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
// import { EyeOff, Eye } from '@element-plus/icons-vue'
import { useAuth } from '@/hooks/useAuth'

const { t } = useI18n()
const router = useRouter()

// 使用共用的认证hooks
const {
  email,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  emailValid,
  passwordValid,
  passwordsMatch,
  passwordError,
  register,
  resetForm
} = useAuth()

const canContinue = computed(() => {
  return emailValid.value && passwordValid.value && passwordsMatch.value
})

const handleRegister = async () => {
  if (!canContinue.value) return

  const result = await register(email.value, password.value)

  if (result.success) {
    ElMessage.success(result.message)
    resetForm()
    router.push('/')
  } else {
    ElMessage.warning(result.message)
    if (result.shouldRedirectToLogin) {
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  }
}

const handleGoogleRegister = () => {
  console.log('Google register')
}

const handleAppleRegister = () => {
  console.log('Apple register')
}

const handleMobileRegister = () => {
  console.log('Mobile register')
}

const goToLogin = () => {
  router.push('/login')
}
</script>
<template>
  <div class="register-container">
    <div class="register-form">
      <h1 class="register-title">{{ t('login.title') }}</h1>
      
      <!-- 邮箱输入 -->
      <div class="form-group">
        <label class="form-label">{{ t('login.email_label') }}</label>
        <el-input
          v-model="email"
          :placeholder="t('login.email_placeholder')"
          class="email-input"
          size="large"
          type="email"
        />
      </div>

      <!-- 密码输入 -->
      <div class="form-group">
        <label class="form-label">Password</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            placeholder="Password"
            class="password-input"
            size="large"
          />
          <el-icon
            class="password-toggle"
            @click="showPassword = !showPassword"
          >
<!--            <Eye v-if="showPassword" />-->
<!--            <EyeOff v-else />-->
          </el-icon>
        </div>
      </div>

      <!-- 重复密码输入 -->
      <div class="form-group">
        <label class="form-label">Retype Password</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="retypePassword"
            :type="showRetypePassword ? 'text' : 'password'"
            placeholder="Password"
            class="password-input"
            size="large"
          />
          <el-icon
            class="password-toggle"
            @click="showRetypePassword = !showRetypePassword"
          >
            <Eye v-if="showRetypePassword" />
            <EyeOff v-else />
          </el-icon>
        </div>
        <div v-if="passwordError" class="error-message">
          * {{ passwordError }}
        </div>
      </div>
      
      <el-button
        type="primary"
        class="continue-btn"
        :class="{ 'disabled': !canContinue }"
        size="large"
        :loading="isLoading"
        :disabled="!canContinue"
        @click="handleRegister"
      >
        Continue
      </el-button>

      <!-- 密码要求提示 -->
      <div class="password-requirements">
        Password must be 6-20 characters long and include at least two of the 
        following: letters, numbers, or symbols. It also cannot be one you've used 
        before.
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.register-container {
  min-height: calc(100vh - 64px); // 减去Header高度
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  padding: 40px 20px;

  // 适配1920px宽度
  @media (min-width: 1920px) {
    max-width: 1920px;
    margin: 0 auto;
  }
}

.register-form {
  border-radius: 16px;
  padding: 48px;
  width: 100%;
  max-width: 560px;
}

.register-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 32px;
  line-height: 1.3;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

:deep(.email-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;

    &:hover {
      border-color: #9ca3af;
    }
  }

  .el-input__inner {
    font-size: 16px;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.password-input-wrapper {
  position: relative;

  .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #9ca3af;

    &:hover {
      color: var(--base-color);
    }
  }
}

:deep(.password-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;
    padding-right: 40px;

    &:hover {
      border-color: #9ca3af;
    }
  }

  .el-input__inner {
    font-size: 16px;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 8px;
  line-height: 1.4;
}

.continue-btn {
  width: 100%;
  border-radius: 36px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  transition: all 0.2s ease;

  &:not(.disabled) {
    background: #1f2937;
    border-color: #1f2937;
    color: white;

    &:hover {
      background: #111827;
      border-color: #111827;
    }
  }

  &.disabled {
    background: #9ca3af;
    border-color: #9ca3af;
    color: white;
    cursor: not-allowed;

    &:hover {
      background: #9ca3af;
      border-color: #9ca3af;
    }
  }
}

.password-requirements {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 16px;
  text-align: center;
}

.agreement-text {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 32px;

  .link {
    color: #059669;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.divider {
  position: relative;
  text-align: center;
  margin: 32px 0;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e7eb;
  }

  .divider-text {
    background: white;
    color: #6b7280;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 500;
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.social-btn {
  width: 100%;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;

  .social-icon {
    display: flex;
    align-items: center;
  }

  &.google-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }

  &.apple-btn {
    background: #000000;
    border: 1px solid #000000;
    color: white;

    &:hover {
      background: #1f2937;
      border-color: #1f2937;
    }
  }

  &.mobile-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }
}

.login-link {
  text-align: center;
  font-size: 14px;
  color: #6b7280;

  .link {
    color: #059669;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
