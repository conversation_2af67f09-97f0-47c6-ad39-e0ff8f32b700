<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Icon } from '@/components/Icon'
import { useAuth } from '@/hooks/useAuth'

const { t } = useI18n()
const router = useRouter()

// 使用共用的认证hooks
const {
  email,
  password,
  isLoading,
  showPassword,
  emailValid,
  checkUserExists,
  login
} = useAuth()

// 表单状态
const currentStep = ref<'email' | 'password'>('email')

const canContinue = computed(() => {
  if (currentStep.value === 'email') {
    return emailValid.value
  } else if (currentStep.value === 'password') {
    return password.value.length > 0
  }
  return false
})

const handleContinue = async () => {
  if (!canContinue.value) return

  if (currentStep.value === 'email') {
    // 检查邮箱是否已注册
    const userExists = await checkUserExists(email.value)
    if (userExists) {
      // 已注册，显示登录密码输入
      currentStep.value = 'password'
    } else {
      // 未注册，跳转到注册页面
      router.push('/register')
    }
  } else if (currentStep.value === 'password') {
    // 使用hooks中的登录方法
    const result = await login(email.value, password.value)
    if (result.success) {
      ElMessage.success(result.message)
      router.push('/')
    } else {
      ElMessage.error(result.message)
    }
  }
}

const handleForgotPassword = () => {
  // 点击忘记密码跳转到忘记密码页面
  router.push('/forgot-password')
}

const handleBackToEmail = () => {
  currentStep.value = 'email'
  password.value = ''
}

const handleGoogleLogin = () => {
  console.log('Google login')
}

const handleAppleLogin = () => {
  console.log('Apple login')
}

const handleMobileLogin = () => {
  console.log('Mobile login')
}

// 监听邮箱变化，重置步骤
watch(email, () => {
  if (currentStep.value !== 'email') {
    currentStep.value = 'email'
    password.value = ''
  }
})
</script>

<template>
  <div class="login-container">
    <div class="login-form">
      <h1 class="login-title">{{ t('login.title') }}</h1>

      <!-- 邮箱输入 -->
      <div class="form-group">
        <label class="form-label">{{ t('login.email_label') }}</label>
        <div class="input-wrapper">
          <el-input
            v-model="email"
            :placeholder="t('login.email_placeholder')"
            class="email-input"
            size="large"
            type="email"
            :readonly="currentStep !== 'email'"
          />
          <el-icon
            v-if="currentStep !== 'email'"
            class="edit-icon"
            @click="handleBackToEmail"
          >
            <Icon icon="ep:edit" />
          </el-icon>
        </div>
      </div>

      <!-- 密码输入 (登录) -->
      <div v-if="currentStep === 'password'" class="form-group">
        <div class="password-header">
          <label class="form-label">Password</label>
          <a href="#" class="forgot-password" @click.prevent="handleForgotPassword">Forgot Password?</a>
        </div>
        <div class="password-input-wrapper">
          <el-input
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            placeholder="••••••••"
            class="password-input"
            size="large"
          />
          <el-icon
            class="password-toggle"
            @click="showPassword = !showPassword"
          >
            <!--            <Eye v-if="showPassword" />-->
            <!--            <EyeOff v-else />-->
          </el-icon>
        </div>
      </div>



      <el-button
        type="primary"
        class="continue-btn"
        :class="{ 'disabled': !canContinue }"
        size="large"
        :loading="isLoading"
        :disabled="!canContinue"
        @click="handleContinue"
      >
        {{ currentStep === 'email' ? t('login.continue') : 'Log In' }}
      </el-button>

      <div class="agreement-text">
        {{ t('login.agreement_text') }}
        <a href="#" class="link">{{ t('login.user_agreement') }}</a>
        {{ t('login.and') }}
        <a href="#" class="link">{{ t('login.privacy_policy') }}</a>
      </div>

      <div class="divider">
        <span class="divider-line"></span>
        <span class="divider-text">{{ t('login.or') }}</span>
      </div>

      <div class="social-login">
        <el-button
          class="social-btn google-btn"
          size="large"
          @click="handleGoogleLogin"
        >
          <Icon icon="svg-icon:google"></Icon>
          {{ t('login.continue_with_google') }}
        </el-button>

        <el-button
          class="social-btn apple-btn"
          size="large"
          @click="handleAppleLogin"
        >
          <Icon icon="svg-icon:apple"></Icon>
          {{ t('login.continue_with_apple') }}
        </el-button>
      </div>

      <!-- 注册链接 -->
      <div class="register-link">
        Don't have an account?
        <router-link to="/register" class="link">Sign Up</router-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.login-container {
  min-height: calc(100vh - 64px); // 减去Header高度
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  padding: 40px 20px;

  // 适配1920px宽度
  @media (min-width: 1920px) {
    max-width: 1920px;
    margin: 0 auto;
  }
}

.login-form {
  padding: 48px;
  width: 100%;
  max-width: 560px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 32px;
  line-height: 1.3;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

:deep(.email-input) {
  .el-input__wrapper {
    border-radius: 8px;
    padding: 12px 16px;
  }

  .el-input__inner {
    font-size: 16px;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.input-wrapper {
  position: relative;

  .edit-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #9ca3af;

    &:hover {
      color: var(--base-color);
    }
  }
}

.password-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .forgot-password {
    color: var(--base-color);
    text-decoration: none;
    font-size: 14px;

    &:hover {
      text-decoration: underline;
    }
  }
}

.password-input-wrapper {
  position: relative;

  .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #9ca3af;

    &:hover {
      color: var(--base-color);
    }
  }
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 8px;
  line-height: 1.4;
}

.password-requirements {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 16px;
  text-align: center;
}

.continue-btn {
  width: 100%;
  border-radius: 36px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  transition: all 0.2s ease;

  &:not(.disabled) {
    background: #1f2937;
    border-color: #1f2937;
    color: white;

    &:hover {
      background: #111827;
      border-color: #111827;
    }
  }

  &.disabled {
    background: #9ca3af;
    border-color: #9ca3af;
    color: white;
    cursor: not-allowed;

    &:hover {
      background: #9ca3af;
      border-color: #9ca3af;
    }
  }
}

.agreement-text {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  line-height: 1.5;
  margin-bottom: 32px;

  .link {
    color: #059669;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.divider {
  position: relative;
  text-align: center;
  margin: 32px 0;
  .divider-line{
    background: #e5e7eb;
    width: 100%;
    height: 1px;
    display: inline-block;
  }
  .divider-text {
    background: white;
    color: #6b7280;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 500;
    position: absolute;
    top:0;
    left: 50%;
    transform: translateX(-50%);
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-btn {
  width: 100%;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;

  .social-icon {
    display: flex;
    align-items: center;
  }

  &.google-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }

  &.apple-btn {
    background: #000000;
    border: 1px solid #000000;
    color: white;

    &:hover {
      background: #1f2937;
      border-color: #1f2937;
    }
  }

  &.mobile-btn {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;

    &:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }
  }
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #6b7280;
  margin-top: 24px;

  .link {
    color: #059669;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
