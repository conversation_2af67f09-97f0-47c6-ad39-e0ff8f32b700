<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
// import { EyeOff, Eye } from '@element-plus/icons-vue'
import { useAuth } from '@/hooks/useAuth'

const { t } = useI18n()
const router = useRouter()

// 使用共用的认证hooks
const {
  email,
  verificationCode,
  password,
  retypePassword,
  isLoading,
  showPassword,
  showRetypePassword,
  codeSent,
  countdown,
  emailValid,
  passwordValid,
  passwordsMatch,
  codeValid,
  passwordError,
  sendVerificationCode,
  resetPassword,
  resetForm
} = useAuth()

// 计算属性
const canSendCode = computed(() => {
  return emailValid.value && countdown.value === 0
})

const canSave = computed(() => {
  return emailValid.value && codeValid.value && passwordValid.value && passwordsMatch.value
})

// 发送验证码
const handleSendCode = async () => {
  if (!canSendCode.value) return

  const result = await sendVerificationCode(email.value)

  if (result.success) {
    ElMessage.success(result.message)
  } else {
    ElMessage.error(result.message)
  }
}

// 保存新密码
const handleSave = async () => {
  if (!canSave.value) return

  const result = await resetPassword(email.value, verificationCode.value, password.value)

  if (result.success) {
    ElMessage.success(result.message)
    resetForm()
    router.push('/login')
  } else {
    ElMessage.error(result.message)
  }
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="forgot-password-container">
    <div class="forgot-password-form">
      <h1 class="forgot-password-title">
        Next, we'll send a verification code to your email. Please enter the 6-digit code we sent.
      </h1>
      
      <!-- 邮箱输入 -->
      <div class="form-group">
        <label class="form-label">Email</label>
        <el-input
          v-model="email"
          placeholder="<EMAIL>"
          class="email-input"
          size="large"
          type="email"
          :readonly="codeSent"
        />
      </div>

      <!-- 验证码输入 -->
      <div class="form-group">
        <label class="form-label">Verification Code</label>
        <div class="verification-wrapper">
          <el-input
            v-model="verificationCode"
            placeholder="Verification code"
            class="verification-input"
            size="large"
            maxlength="6"
          />
          <el-button
            class="send-code-btn"
            :class="{ 'disabled': !canSendCode }"
            :disabled="!canSendCode"
            :loading="isLoading && !codeSent"
            @click="handleSendCode"
          >
            {{ countdown > 0 ? `${countdown}s` : 'Send code' }}
          </el-button>
        </div>
      </div>

      <!-- 密码输入 -->
      <div class="form-group">
        <label class="form-label">Password</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            placeholder="Password"
            class="password-input"
            size="large"
          />
          <el-icon 
            class="password-toggle"
            @click="showPassword = !showPassword"
          >
<!--            <Eye v-if="showPassword" />-->
<!--            <EyeOff v-else />-->
          </el-icon>
        </div>
      </div>

      <!-- 重复密码输入 -->
      <div class="form-group">
        <label class="form-label">Retype Password</label>
        <div class="password-input-wrapper">
          <el-input
            v-model="retypePassword"
            :type="showRetypePassword ? 'text' : 'password'"
            placeholder="Password"
            class="password-input"
            size="large"
          />
          <el-icon 
            class="password-toggle"
            @click="showRetypePassword = !showRetypePassword"
          >
<!--            <Eye v-if="showRetypePassword" />-->
<!--            <EyeOff v-else />-->
          </el-icon>
        </div>
        <div v-if="passwordError" class="error-message">
          * {{ passwordError }}
        </div>
      </div>
      
      <el-button
        type="primary"
        class="save-btn"
        :class="{ 'disabled': !canSave }"
        size="large"
        :loading="isLoading && codeSent"
        :disabled="!canSave"
        @click="handleSave"
      >
        Save
      </el-button>

      <!-- 密码要求提示 -->
      <div class="password-requirements">
        Password must be 6-20 characters long and include at least two of the 
        following: letters, numbers, or symbols. It also cannot be one you've used 
        before.
      </div>

      <!-- 返回登录链接 -->
      <div class="login-link">
        Remember your password? 
        <a href="#" @click.prevent="goToLogin" class="link">Back to Login</a>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.forgot-password-container {
  min-height: calc(100vh - 64px); // 减去Header高度
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  padding: 40px 20px;

  // 适配1920px宽度
  @media (min-width: 1920px) {
    max-width: 1920px;
    margin: 0 auto;
  }
}

.forgot-password-form {
  background: white;
  border-radius: 16px;
  padding: 48px;
  width: 100%;
  max-width: 500px; // 比登录表单稍宽一些
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.forgot-password-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 32px;
  line-height: 1.4;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

:deep(.email-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;

    &:hover {
      border-color: #9ca3af;
    }
  }

  .el-input__inner {
    font-size: 16px;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.verification-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;

  .verification-input {
    flex: 1;
  }

  .send-code-btn {
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    min-width: 100px;

    &:not(.disabled) {
      background: var(--base-color);
      border-color: var(--base-color);
      color: white;

      &:hover {
        background: var(--primary-color);
        border-color: var(--primary-color);
      }
    }

    &.disabled {
      background: #9ca3af;
      border-color: #9ca3af;
      color: white;
      cursor: not-allowed;
    }
  }
}

:deep(.verification-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;

    &:hover {
      border-color: #9ca3af;
    }
  }

  .el-input__inner {
    font-size: 16px;
    text-align: center;
    letter-spacing: 2px;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.password-input-wrapper {
  position: relative;

  .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #9ca3af;

    &:hover {
      color: var(--base-color);
    }
  }
}

:deep(.password-input) {
  .el-input__wrapper {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 12px 16px;
    padding-right: 40px;

    &:hover {
      border-color: #9ca3af;
    }
  }

  .el-input__inner {
    font-size: 16px;

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 8px;
  line-height: 1.4;
}

.save-btn {
  width: 100%;
  border-radius: 36px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  transition: all 0.2s ease;

  &:not(.disabled) {
    background: #9ca3af;
    border-color: #9ca3af;
    color: white;

    &:hover {
      background: #6b7280;
      border-color: #6b7280;
    }
  }

  &.disabled {
    background: #9ca3af;
    border-color: #9ca3af;
    color: white;
    cursor: not-allowed;

    &:hover {
      background: #9ca3af;
      border-color: #9ca3af;
    }
  }
}

.password-requirements {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 24px;
  text-align: center;
}

.login-link {
  text-align: center;
  font-size: 14px;
  color: #6b7280;

  .link {
    color: #059669;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
