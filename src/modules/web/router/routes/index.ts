import LayoutWeb from '@web/layout/index.vue'

export default {
  path: '/',
  redirect: {
    name: 'home',
  },
  component: LayoutWeb,
  children: [
    {
      path: '',
      name: 'home',
      meta: { title: 'nav.home' },
      component: () => import('@web/views/HomeView.vue'),
    },
    {
      path: 'about',
      name: 'about',
      meta: { title: 'nav.about' },
      component: () => import('@web/views/AboutView.vue'),
    },
    {
      path: 'shop-plans',
      name: 'shop-plans',
      meta: { title: 'nav.shop_plans' },
      component: () => import('@web/views/AboutView.vue'), // 临时使用AboutView
    },
    {
      path: 'my-esims',
      name: 'my-esims',
      meta: { title: 'nav.my_esims' },
      component: () => import('@web/views/AboutView.vue'), // 临时使用AboutView
    },
    {
      path: 'login',
      name: 'login',
      meta: { title: 'nav.login' },
      component: () => import('@web/views/Login/Login.vue'),
    },
    {
      path: 'register',
      name: 'register',
      meta: { title: 'Register' },
      component: () => import('@web/views/Register/Register.vue'),
    },
    {
      path: 'forgot-password',
      name: 'forgot-password',
      meta: { title: 'Forgot Password' },
      component: () => import('@web/views/ForgotPassword/ForgotPassword.vue'),
    },
  ],
}
