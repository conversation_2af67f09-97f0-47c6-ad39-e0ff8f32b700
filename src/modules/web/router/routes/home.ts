import LayoutWeb from '@web/layout/index.vue'

export default {
  path: '/',
  redirect: {
    name: 'home',
  },
  component: LayoutWeb,
  children: [
    {
      path: '',
      name: 'home',
      meta: { title: 'nav.home' },
      component: () => import('@web/views/HomeView.vue'),
    },
    {
      path: 'about',
      name: 'about',
      meta: { title: 'nav.about' },
      component: () => import('@web/views/AboutView.vue'),
    },
  ],
}
