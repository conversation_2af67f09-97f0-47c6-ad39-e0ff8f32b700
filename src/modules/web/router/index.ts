import { createRouter, createWebHashHistory } from 'vue-router'
import { metaPageTitle } from '@/utils/metaTitle'
import permission from '@/utils/permission'
import homeRoutes from './routes'

const routes = [
  {
    path: '/',
    redirect: {
      name: 'home',
    },
  },
  homeRoutes,
]

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
})

metaPageTitle(router)
permission(router)

export default router
