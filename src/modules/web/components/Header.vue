<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Search, ArrowUp } from '@element-plus/icons-vue'

const { t } = useI18n()
const router = useRouter()

const searchValue = ref('')

const handleSearch = () => {
  console.log('Search:', searchValue.value)
}

const handleLogin = () => {
  router.push('/login')
}

const navItems = [
  { key: 'shop_plans', path: '/shop-plans' },
  { key: 'my_esims', path: '/my-esims' },
  { key: 'about_us', path: '/about' },
]
</script>

<template>
  <header class="header">
    <div class="header-container">
      <!-- Logo -->
      <div class="logo">
        <router-link to="/" class="logo-link">
           <img src="@/assets/images/Logo.svg" alt="logo">
        </router-link>
      </div>

      <!-- Search -->
      <div class="search-container">
        <div class="search-box">
          <el-icon class="search-icon">
            <Search />
          </el-icon>
          <el-input
            v-model="searchValue"
            :placeholder="t('header.search_placeholder')"
            class="search-input"
            @keyup.enter="handleSearch"
          />
        </div>
      </div>

      <!-- Navigation -->
      <nav class="nav">
        <router-link
          v-for="item in navItems"
          :key="item.key"
          :to="item.path"
          class="nav-link"
        >
          {{ t(`nav.${item.key}`) }}
        </router-link>
      </nav>

      <!-- Login Button -->
      <div class="login-section">
        <el-button
          type="primary"
          class="login-btn"
          @click="handleLogin"
        >

          {{ t('nav.login') }}
          <el-icon class="login-icon">
            <el-icon-top-right />
          </el-icon>
        </el-button>
      </div>
    </div>
  </header>
</template>

<style scoped lang="scss">
.header {
  background: #ffffff;
  border-bottom: 1px solid #E0E0E0;
  padding: 0 24px;
  height: var(--header-height);
  
  .header-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 32px;
    height: 100%;
  }
}

.logo {
  .logo-link {
    font-size: 24px;
    font-weight: bold;
    color: #1f2937;
    text-decoration: none;
    height: var(--logo-height);
    line-height: var(--logo-height);
    img{
      height: var(--logo-height);

    }
    
    &:hover {
      color: #059669;
    }
  }
}

.search-container {
  flex: 1;
  max-width: 400px;
  
  .search-box {
    position: relative;
    
    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #9ca3af;
      z-index: 1;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #ffffff;
      border: 1px solid #e5e7eb;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

      &:hover {
        background-color: #f9fafb;
        border-color: var(--base-color);
        color: var(--base-color);
      }
    }
    
    :deep(.search-input) {
      .el-input__wrapper {
        padding-left: 56px;
        border-radius: 28px;
        background-color: #f3f4f6;
        border: 1px solid #e5e7eb;
        height: 56px;

        /* hover样式使用全局定义 */
      }
      
      .el-input__inner {
        background: transparent;
        border: none;
        
        &::placeholder {
          color: #9ca3af;
        }
      }
    }
  }
}

.nav {
  display: flex;
  gap: 32px;
  
  .nav-link {
    color:var(--primary-color);
    text-decoration: none;
    transition: color 0.2s;
    font-size:20px;
    white-space: nowrap;
    font-weight: bold;
    
    &:hover {
      color: var(--base-color);
    }
    
    &.router-link-active {
      color:var(--base-color);
    }
  }
}

.login-section {
  .login-btn {
    background: var(--base-color);
    border-radius: 20px;
    padding: 8px 24px;
    font-weight: 500;
    border-color: var(--base-color);
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover {
      background: var(--base-color);
      border-color: var(--base-color);
    }

    .login-icon {
      /* 登录按钮中的图标不需要圆圈，使用全局样式会自动应用 */
    }
  }
}
</style>
