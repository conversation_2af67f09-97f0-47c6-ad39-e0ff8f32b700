// PC端 Element Plus 组件按需导入配置
// 只导入项目中实际使用的 Element Plus 组件

import { App } from 'vue'

// 按需导入实际使用的 Element Plus 组件
import {
  // 基础组件
  ElButton,         // el-button - 按钮组件
  ElIcon,           // el-icon - 图标组件
  ElInput,          // el-input - 输入框组件
  ElCol,            // el-col - 栅格列组件
  
  // 表单组件
  ElForm,           // el-form - 表单组件
  ElFormItem,       // el-form-item - 表单项组件
  ElSelect,         // el-select - 选择器组件
  ElOption,         // el-option - 选择器选项组件
  ElSelectV2,       // el-select-v2 - 虚拟化选择器
  ElDatePicker,     // el-date-picker - 日期选择器
  ElTimePicker,     // el-time-picker - 时间选择器
  ElCheckbox,       // el-checkbox - 复选框
  ElCheckboxGroup,  // el-checkbox-group - 复选框组
  ElRadio,          // el-radio - 单选框
  ElRadioGroup,     // el-radio-group - 单选框组
  ElSwitch,         // el-switch - 开关
  
  // 配置组件
  ElConfigProvider, // el-config-provider - 全局配置
  
  // 反馈组件
  ElMessage,        // 消息提示
  
  // 其他组件
  ElSkeleton,       // el-skeleton - 骨架屏
  ElSegmented,      // el-segmented - 分段控制器
} from 'element-plus'

// 导入图标组件
import {
  Search,           // 搜索图标
  TopRight,         // 右上角箭头图标
} from '@element-plus/icons-vue'

// 需要注册的组件列表
const components = [
  ElButton,
  ElIcon,
  ElInput,
  ElCol,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElSelectV2,
  ElDatePicker,
  ElTimePicker,
  ElCheckbox,
  ElCheckboxGroup,
  ElRadio,
  ElRadioGroup,
  ElSwitch,
  ElConfigProvider,
  ElSkeleton,
  ElSegmented,
]

// 需要注册的图标组件
const iconComponents = [
  { name: 'Search', component: Search },
  { name: 'TopRight', component: TopRight },
  { name: 'ElIconTopRight', component: TopRight }, // 兼容自动生成的名称
]

// 注册组件的函数
export function setupElementPlus(app: App) {
  // 注册普通组件
  components.forEach(component => {
    app.component(component.name, component)
  })
  
  // 注册图标组件
  iconComponents.forEach(({ name, component }) => {
    app.component(name, component)
  })
}

// 导出消息组件，方便在组件中使用
export {
  ElMessage,
}

// 默认导出设置函数
export default setupElementPlus
