<script setup lang="ts">
import { RouterView } from 'vue-router'
import Header from '@web/components/Header.vue'
</script>

<template>
  <div class="layout">
    <Header />
    <main class="main-content">
      <RouterView />
    </main>
  </div>
</template>

<style scoped lang="scss">
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  // 适配1920px宽度
  @media (min-width: 1920px) {
    max-width: 1920px;
    margin: 0 auto;
  }
}

.main-content {
  flex: 1;
}
</style>
